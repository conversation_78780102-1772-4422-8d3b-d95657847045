package com.illumio.data;

import com.illumio.data.components.EnrichedFlowWriter;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.LabelLookup;
import com.illumio.data.configuration.LabelRecommendationConfig;
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.MetricsUtil;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.Optional;

@Slf4j
@Component
public class LabelRecommendationPipeline {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final DecoratedFlowReader decoratedFlowReader;
    private final LabelLookup labelLookup;
    private final EnrichedFlowWriter enrichedFlowWriter;
    private final KafkaSender<String, String> kafkaSender;
    private final String sinkTopic;
    private final MetricsUtil metricsUtil;

    private Disposable disposable;

    public LabelRecommendationPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            DecoratedFlowReader decoratedFlowReader,
            LabelLookup labelLookup,
            EnrichedFlowWriter enrichedFlowWriter,
            KafkaSender<String, String> kafkaSender,
            LabelRecommendationConfig labelRecommendationConfig,
            MetricsUtil metricsUtil) {
        this.kafkaReceiver = kafkaReceiver;
        this.decoratedFlowReader = decoratedFlowReader;
        this.labelLookup = labelLookup;
        this.enrichedFlowWriter = enrichedFlowWriter;
        this.kafkaSender = kafkaSender;
        this.sinkTopic = getSinkTopic(labelRecommendationConfig);
        this.metricsUtil = metricsUtil;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param labelRecommendationConfig object which contains application specific configuration
     * @return Configured downstream Kafka topic name
     */
    private String getSinkTopic(LabelRecommendationConfig labelRecommendationConfig) {
        return Optional.of(labelRecommendationConfig)
                .map(LabelRecommendationConfig::getKafkaSenderConfig)
                .map(LabelRecommendationConfig.KafkaSenderConfig::getTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No topic configured. Please make sure "
                                                + "labelRecommendation.kafkaSenderConfig.topic is set."));
    }

    public void start() {
        this.disposable = startInternal().subscribe();
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(
                                        Retry.backoff(3, Duration.ofSeconds(10L))
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from kafka {}",
                                                                        retrySignal.toString())))
                                .concatMap(r -> r)
                                .doOnNext(r -> metricsUtil.incrementRecordsIncoming())
                                .flatMap(this::processConsumerRecord))
                .doOnNext(r -> metricsUtil.incrementRecordsOutgoing())
                .retryWhen(
                        Retry.backoff(3, Duration.ofSeconds(10L))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error sending to kafka {}",
                                                        retrySignal.toString())));
    }

    public Mono<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.debug("Processing record {}", consumerRecord))
                .publishOn(Schedulers.parallel())
                .flatMap(
                        __ ->
                                decoratedFlowReader
                                        .readTree(consumerRecord.value())
                                        .doOnError(
                                                throwable -> {
                                                        log.error(
                                                                "Error parsing record {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable);
                                                        metricsUtil.incrementRecordsWithErrors(
                                                                Attributes.of(
                                                                        AttributeKey.stringKey(FlowDataValidator.ILLUMIO_TENANT_ID_KEY),
                                                                        "Unknown"));
                                                })
                                        .doOnNext(r -> {
                                            // Extract tenant ID for outgoing metrics
                                            String tenantId = FlowDataValidator.extractTenantId(r);
                                            Attributes tenantAttributes = Attributes.builder()
                                                    .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                                                    .build();
                                            metricsUtil.incrementAcceptedRecordPerTenant(tenantAttributes);
                                            FlowDataValidator.ValidationResult validationResult =
                                                    FlowDataValidator.validate(r);
                                            if (!validationResult.isValid()) {
                                                metricsUtil.incrementRecordsWithErrors(
                                                        Attributes.builder()
                                                                .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                                                                .put("error_code", validationResult.getErrorCode())
                                                                .build());
                                                log.error("Invalid JSON: {}, detail: {}", r, validationResult.getDetailMessage());
                                            }
                                        })
                                        .flatMap(labelLookup::enrichWithLabel)
                                        .doOnNext(r -> {
                                            String tenantId = FlowDataValidator.extractTenantId(r);
                                            Attributes tenantAttributes = Attributes.builder()
                                                    .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                                                    .build();
                                            metricsUtil.incrementOutgoingRecordPerTenant(tenantAttributes);
                                        })
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Error enriching classification flow with label lookup {}: {}",
                                                                consumerRecordString(consumerRecord),
                                                                consumerRecord.value(),
                                                                throwable))
                                        .flatMap(enrichedFlowWriter::writeTreeAsString)
                                        .doOnNext(enrichedFlow -> log.debug("Sending json record: {}", enrichedFlow)))
                .doOnError(throwable -> log.warn("Error parsing the record or adding label to the record."))
                .onErrorReturn(consumerRecord.value())
                .flatMap(newValue -> createSenderRecord(sinkTopic, consumerRecord, newValue));
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            String topic, ConsumerRecord<String, String> consumerRecord, String enrichedRecordString) {
        return Mono.just(
                SenderRecord.create(
                        new ProducerRecord<>(
                                topic,
                                consumerRecord.key(),
                                enrichedRecordString),
                        consumerRecordString(consumerRecord)));
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
